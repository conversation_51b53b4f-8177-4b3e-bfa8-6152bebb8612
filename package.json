{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "NODE_NO_WARNINGS=1 next dev", "build": "NODE_NO_WARNINGS=1 next build", "start": "NODE_NO_WARNINGS=1 next start", "lint": "next lint"}, "dependencies": {"autoprefixer": "^10.4.21", "firebase": "^10.12.2", "lucide-react": "^0.400.0", "next": "14.2.5", "react": "^18", "react-dom": "^18", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.13", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}