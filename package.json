{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.2.5", "react": "^18", "react-dom": "^18", "firebase": "^10.12.2", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "lucide-react": "^0.400.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "tailwindcss": "^3.4.1", "eslint": "^8", "eslint-config-next": "14.2.5", "@tailwindcss/typography": "^0.5.13"}}