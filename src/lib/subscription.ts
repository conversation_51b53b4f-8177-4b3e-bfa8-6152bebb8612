import { collection, addDoc, query, where, getDocs } from 'firebase/firestore'
import { db } from './firebase'
import { Subscriber } from '@/types'

export class SubscriptionService {
  private static readonly COLLECTION_NAME = 'subscribers'

  static async subscribe(email: string, preferences?: Subscriber['preferences']): Promise<string> {
    try {
      // Check if email already exists
      const existingSubscriber = await this.getSubscriberByEmail(email)
      if (existingSubscriber) {
        throw new Error('Email is already subscribed')
      }

      // Add new subscriber
      const subscriberData: Omit<Subscriber, 'id'> = {
        email: email.toLowerCase().trim(),
        subscribedAt: new Date(),
        preferences: preferences || {
          journalUpdates: true,
          musicReleases: true,
          visualUpdates: true,
        }
      }

      const docRef = await addDoc(collection(db, this.COLLECTION_NAME), subscriberData)
      return docRef.id
    } catch (error) {
      console.error('Error subscribing user:', error)
      throw error
    }
  }

  static async getSubscriberByEmail(email: string): Promise<Subscriber | null> {
    try {
      const q = query(
        collection(db, this.COLLECTION_NAME),
        where('email', '==', email.toLowerCase().trim())
      )
      
      const querySnapshot = await getDocs(q)
      
      if (querySnapshot.empty) {
        return null
      }

      const doc = querySnapshot.docs[0]
      return {
        id: doc.id,
        ...doc.data()
      } as Subscriber
    } catch (error) {
      console.error('Error getting subscriber:', error)
      throw error
    }
  }

  static async getAllSubscribers(): Promise<Subscriber[]> {
    try {
      const querySnapshot = await getDocs(collection(db, this.COLLECTION_NAME))
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Subscriber[]
    } catch (error) {
      console.error('Error getting all subscribers:', error)
      throw error
    }
  }

  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }
}
