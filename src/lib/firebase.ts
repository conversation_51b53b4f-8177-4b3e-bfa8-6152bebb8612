import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";
import { getAuth } from "firebase/auth";

const firebaseConfig = {
  apiKey: "AIzaSyCic5BsUrCnML_6uwlwu3ATLAy7eIpg2S0",
  authDomain: "teeseia.firebaseapp.com",
  projectId: "teeseia",
  storageBucket: "teeseia.firebasestorage.app",
  messagingSenderId: "1078082158309",
  appId: "1:1078082158309:web:ce9e50b2429dd2cd795e0c",
  measurementId: "G-GV5QPH0QKM"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const db = getFirestore(app);
export const storage = getStorage(app);
export const auth = getAuth(app);

// Initialize Analytics (only in browser)
export const analytics = typeof window !== 'undefined' ? getAnalytics(app) : null;

export default app;
