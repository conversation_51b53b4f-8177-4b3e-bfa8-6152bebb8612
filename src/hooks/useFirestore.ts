'use client'

import { useState, useEffect } from 'react'
import { 
  collection, 
  query, 
  orderBy, 
  limit, 
  getDocs, 
  doc, 
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  where,
  DocumentData,
  QueryConstraint
} from 'firebase/firestore'
import { db } from '@/lib/firebase'

export function useFirestoreCollection<T = DocumentData>(
  collectionName: string,
  constraints: QueryConstraint[] = []
) {
  const [data, setData] = useState<T[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const collectionRef = collection(db, collectionName)
        const q = query(collectionRef, ...constraints)
        const querySnapshot = await getDocs(q)
        
        const documents = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as T[]
        
        setData(documents)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [collectionName, JSON.stringify(constraints)])

  return { data, loading, error }
}

export function useFirestoreDocument<T = DocumentData>(
  collectionName: string,
  documentId: string | null
) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!documentId) {
      setData(null)
      setLoading(false)
      return
    }

    const fetchDocument = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const docRef = doc(db, collectionName, documentId)
        const docSnap = await getDoc(docRef)
        
        if (docSnap.exists()) {
          setData({ id: docSnap.id, ...docSnap.data() } as T)
        } else {
          setData(null)
          setError('Document not found')
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchDocument()
  }, [collectionName, documentId])

  return { data, loading, error }
}

export function useFirestoreActions(collectionName: string) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const addDocument = async (data: any) => {
    try {
      setLoading(true)
      setError(null)
      
      const collectionRef = collection(db, collectionName)
      const docRef = await addDoc(collectionRef, {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      
      return docRef.id
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const updateDocument = async (documentId: string, data: any) => {
    try {
      setLoading(true)
      setError(null)
      
      const docRef = doc(db, collectionName, documentId)
      await updateDoc(docRef, {
        ...data,
        updatedAt: new Date()
      })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const deleteDocument = async (documentId: string) => {
    try {
      setLoading(true)
      setError(null)
      
      const docRef = doc(db, collectionName, documentId)
      await deleteDoc(docRef)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  return {
    addDocument,
    updateDocument,
    deleteDocument,
    loading,
    error
  }
}
