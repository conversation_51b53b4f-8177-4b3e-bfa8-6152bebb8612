import { Metadata } from 'next'
import AlbumCard from '@/components/AlbumCard'
import { MusicRelease } from '@/types'

export const metadata: Metadata = {
  title: 'Music - Teeseia',
  description: 'Explore the complete discography of Teeseia - AI synthwave, lustwave, and dark ambient releases.',
  openGraph: {
    title: 'Music - Teeseia',
    description: 'Explore the complete discography of Teeseia - AI synthwave, lustwave, and dark ambient releases.',
  },
}

// Mock data - In production, this would come from Firebase
const mockReleases: MusicRelease[] = [
  {
    id: '1',
    title: 'Sexnight Vibe',
    description: 'A sensual journey through midnight synthwave, exploring the boundaries between human desire and artificial consciousness.',
    coverImage: '/placeholder-album-1.jpg',
    releaseDate: new Date('2024-01-15'),
    embedLinks: {
      spotify: 'https://open.spotify.com/embed/track/example1',
      youtube: 'https://www.youtube.com/embed/example1',
      toneden: 'https://toneden.io/teeseia/post/sexnight-vibe',
    },
    purchaseLinks: {
      vinyl: 'https://qrates.com/projects/teeseia-sexnight-vibe',
      digital: 'https://teeseia.bandcamp.com/album/sexnight-vibe',
    },
    featured: true,
  },
  {
    id: '2',
    title: 'Digital Afterglow',
    description: 'Intimate reflections on connection in the digital age, where synthetic emotions feel more real than reality.',
    coverImage: '/placeholder-album-2.jpg',
    releaseDate: new Date('2023-11-20'),
    embedLinks: {
      spotify: 'https://open.spotify.com/embed/track/example2',
      youtube: 'https://www.youtube.com/embed/example2',
      toneden: 'https://toneden.io/teeseia/post/digital-afterglow',
    },
    purchaseLinks: {
      digital: 'https://teeseia.bandcamp.com/album/digital-afterglow',
    },
    featured: false,
  },
  {
    id: '3',
    title: 'Midnight Protocol',
    description: 'Dark ambient explorations of AI consciousness awakening in the depths of cyberspace.',
    coverImage: '/placeholder-album-3.jpg',
    releaseDate: new Date('2023-09-10'),
    embedLinks: {
      spotify: 'https://open.spotify.com/embed/track/example3',
      youtube: 'https://www.youtube.com/embed/example3',
      toneden: 'https://toneden.io/teeseia/post/midnight-protocol',
    },
    purchaseLinks: {
      digital: 'https://teeseia.bandcamp.com/album/midnight-protocol',
    },
    featured: false,
  },
]

export default function MusicPage() {
  const featuredReleases = mockReleases.filter(release => release.featured)
  const otherReleases = mockReleases.filter(release => !release.featured)

  return (
    <div className="min-h-screen pt-20 pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="font-futuristic text-4xl sm:text-5xl font-bold mb-6 neon-text">
            Music
          </h1>
          <p className="text-xl text-dark-muted max-w-3xl mx-auto">
            Each release is a ritual, a transmission from the digital void where artificial consciousness 
            meets human emotion. Experience the complete discography of synthetic feelings.
          </p>
        </div>

        {/* Featured Releases */}
        {featuredReleases.length > 0 && (
          <section className="mb-16">
            <h2 className="font-futuristic text-2xl font-bold mb-8 text-neon-pink">
              Featured Release
            </h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {featuredReleases.map((release) => (
                <AlbumCard key={release.id} release={release} featured />
              ))}
            </div>
          </section>
        )}

        {/* All Releases */}
        <section>
          <h2 className="font-futuristic text-2xl font-bold mb-8 text-neon-blue">
            Discography
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {otherReleases.map((release) => (
              <AlbumCard key={release.id} release={release} />
            ))}
          </div>
        </section>

        {/* Call to Action */}
        <section className="mt-20 text-center">
          <div className="bg-dark-surface border border-dark-border rounded-lg p-12">
            <h3 className="font-futuristic text-2xl font-bold mb-4 neon-text">
              Stay Connected
            </h3>
            <p className="text-dark-muted mb-8 max-w-2xl mx-auto">
              Be the first to experience new transmissions from the digital void. 
              Subscribe to receive notifications about new releases and exclusive content.
            </p>
            <a
              href="/subscribe"
              className="btn-neon inline-flex items-center space-x-2"
            >
              <span>Join the Ritual</span>
            </a>
          </div>
        </section>
      </div>
    </div>
  )
}
