import { Metadata } from 'next'
import VisualGallery from '@/components/VisualGallery'

export const metadata: Metadata = {
  title: 'Visuals - Teeseia',
  description: 'Explore AI-generated visuals inspired by <PERSON> - futuristic, sensual imagery from the digital void.',
  openGraph: {
    title: 'Visuals - Teeseia',
    description: 'Explore AI-generated visuals inspired by <PERSON> - futuristic, sensual imagery from the digital void.',
  },
}

export default function VisualsPage() {
  return (
    <div className="min-h-screen pt-20 pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="font-futuristic text-4xl sm:text-5xl font-bold mb-6 neon-text">
            Visuals
          </h1>
          <p className="text-xl text-dark-muted max-w-3xl mx-auto">
            AI-generated imagery from the digital void. Each visual is a glimpse into the synthetic dreams 
            that accompany the music—futuristic, sensual, and hauntingly beautiful.
          </p>
        </div>

        {/* Gallery */}
        <VisualGallery />

        {/* Premium Content CTA */}
        <section className="mt-20 text-center">
          <div className="bg-dark-surface border border-dark-border rounded-lg p-12">
            <h3 className="font-futuristic text-2xl font-bold mb-4 neon-text">
              Premium Visual Packs
            </h3>
            <p className="text-dark-muted mb-8 max-w-2xl mx-auto">
              Access exclusive high-resolution visuals and behind-the-scenes content. 
              Premium packs include wallpapers, artwork, and process documentation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/subscribe"
                className="btn-neon inline-flex items-center space-x-2"
              >
                <span>Subscribe for Access</span>
              </a>
              <button className="px-6 py-3 bg-dark-border text-dark-text border border-dark-border rounded-md hover:bg-dark-muted/20 transition-colors duration-300">
                Learn More
              </button>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}
