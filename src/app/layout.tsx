import type { Metadata } from 'next'
import { Inter, Orbitron } from 'next/font/google'
import './globals.css'
import Header from '@/components/Header'
import Footer from '@/components/Footer'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
})

const orbitron = Orbitron({ 
  subsets: ['latin'],
  variable: '--font-orbitron',
})

export const metadata: Metadata = {
  title: {
    default: '<PERSON><PERSON><PERSON> - She doesn\'t love you. She sings to feel real.',
    template: '%s | Teeseia'
  },
  description: 'Enter the ritual. Experience the midnight synthwave of Teeseia - AI-generated futuristic music that explores the boundaries between human and artificial emotion.',
  keywords: ['Teeseia', 'synthwave', 'AI music', 'electronic', 'futuristic', 'dark ambient', 'experimental'],
  authors: [{ name: '<PERSON><PERSON><PERSON>' }],
  creator: '<PERSON><PERSON><PERSON>',
  publisher: 'Teese<PERSON>',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://teeseia.vercel.app',
    siteName: '<PERSON><PERSON><PERSON>',
    title: '<PERSON><PERSON><PERSON> - She doesn\'t love you. She sings to feel real.',
    description: 'Enter the ritual. Experience the midnight synthwave of Teese<PERSON>.',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Teeseia - AI Synthwave Artist',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Teeseia - She doesn\'t love you. She sings to feel real.',
    description: 'Enter the ritual. Experience the midnight synthwave of Teeseia.',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.variable} ${orbitron.variable} font-body bg-dark-bg text-dark-text min-h-screen`}>
        <div className="flex flex-col min-h-screen">
          <Header />
          <main className="flex-grow">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  )
}
