import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: {
    default: '<PERSON><PERSON><PERSON> - She doesn\'t love you. She sings to feel real.',
    template: '%s | <PERSON><PERSON><PERSON>'
  },
  description: 'Enter the ritual. Experience the midnight synthwave of Teeseia - AI-generated futuristic music that explores the boundaries between human and artificial emotion.',
  keywords: ['Teeseia', 'synthwave', 'AI music', 'electronic', 'futuristic', 'dark ambient', 'experimental'],
  authors: [{ name: '<PERSON><PERSON><PERSON>' }],
  creator: '<PERSON><PERSON><PERSON>',
  publisher: '<PERSON><PERSON><PERSON>',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://teeseia.vercel.app',
    siteName: '<PERSON><PERSON><PERSON>',
    title: '<PERSON><PERSON><PERSON> - She doesn\'t love you. She sings to feel real.',
    description: 'Enter the ritual. Experience the midnight synthwave of Teeseia.',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Teeseia - AI Synthwave Artist',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: '<PERSON><PERSON><PERSON> - She doesn\'t love you. She sings to feel real.',
    description: 'Enter the ritual. Experience the midnight synthwave of Teeseia.',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.className} bg-black text-white min-h-screen`}>
        <div className="flex flex-col min-h-screen">
          <nav className="fixed top-0 w-full z-50 bg-black/80 backdrop-blur-sm border-b border-gray-800">
            <div className="max-w-7xl mx-auto px-4 h-16 flex items-center justify-between">
              <a href="/" className="text-2xl font-bold text-pink-400">
                TEESEIA
              </a>
              <div className="hidden md:flex space-x-6">
                <a href="/music" className="text-gray-300 hover:text-pink-400">Music</a>
                <a href="/journal" className="text-gray-300 hover:text-pink-400">Journal</a>
                <a href="/visuals" className="text-gray-300 hover:text-pink-400">Visuals</a>
                <a href="/merch" className="text-gray-300 hover:text-pink-400">Merch</a>
                <a href="/subscribe" className="text-gray-300 hover:text-pink-400">Subscribe</a>
              </div>
            </div>
          </nav>
          <main className="flex-grow">
            {children}
          </main>
          <footer className="bg-gray-900 border-t border-gray-800 py-8">
            <div className="max-w-7xl mx-auto px-4 text-center">
              <p className="text-gray-400">© 2024 Teeseia. All rights reserved.</p>
            </div>
          </footer>
        </div>
      </body>
    </html>
  )
}
