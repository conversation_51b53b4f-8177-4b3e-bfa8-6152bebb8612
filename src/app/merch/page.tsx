import { Metadata } from 'next'
import { ExternalLink, ShoppingCart, Disc, Package } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Merch - Teeseia',
  description: 'Physical manifestations of the digital void. Vinyl releases, limited edition merchandise, and exclusive bundles.',
  openGraph: {
    title: 'Merch - Teeseia',
    description: 'Physical manifestations of the digital void. Vinyl releases, limited edition merchandise, and exclusive bundles.',
  },
}

export default function MerchPage() {
  const vinylReleases = [
    {
      id: '1',
      title: 'Sexnight Vibe - Limited Edition Vinyl',
      description: 'First pressing of 500 copies on neon pink vinyl. Includes exclusive artwork and digital download code.',
      price: '$35.00',
      status: 'Available',
      platform: 'Qrates',
      link: 'https://qrates.com/projects/teeseia-sexnight-vibe',
      image: '/placeholder-vinyl-1.jpg',
    },
    {
      id: '2',
      title: 'Digital Afterglow - Collector\'s Edition',
      description: 'Limited run of 300 copies on translucent blue vinyl with holographic sleeve.',
      price: '$40.00',
      status: 'Pre-order',
      platform: 'Qrates',
      link: 'https://qrates.com/projects/teeseia-digital-afterglow',
      image: '/placeholder-vinyl-2.jpg',
    },
  ]

  const digitalBundles = [
    {
      id: '1',
      title: 'Complete Discography Bundle',
      description: 'All released tracks in high-quality FLAC format, plus exclusive B-sides and remixes.',
      price: '$25.00',
      platform: 'Bandcamp',
      link: 'https://teeseia.bandcamp.com/album/complete-bundle',
    },
    {
      id: '2',
      title: 'Visual + Audio Pack',
      description: 'Latest release with matching AI-generated visuals, wallpapers, and behind-the-scenes content.',
      price: '$15.00',
      platform: 'Bandcamp',
      link: 'https://teeseia.bandcamp.com/album/visual-audio-pack',
    },
  ]

  return (
    <div className="min-h-screen pt-20 pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="font-futuristic text-4xl sm:text-5xl font-bold mb-6 neon-text">
            Merch
          </h1>
          <p className="text-xl text-dark-muted max-w-3xl mx-auto">
            Physical manifestations of the digital void. Limited edition vinyl releases, 
            exclusive bundles, and artifacts from the synthetic realm.
          </p>
        </div>

        {/* Vinyl Releases */}
        <section className="mb-20">
          <div className="flex items-center space-x-3 mb-8">
            <Disc className="w-8 h-8 text-neon-pink" />
            <h2 className="font-futuristic text-3xl font-bold text-neon-pink">
              Vinyl Releases
            </h2>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {vinylReleases.map((release) => (
              <div
                key={release.id}
                className="bg-dark-surface border border-dark-border rounded-lg overflow-hidden hover:border-neon-pink/50 transition-all duration-300"
              >
                {/* Image */}
                <div className="aspect-square bg-gradient-to-br from-neon-pink/20 to-neon-blue/20 flex items-center justify-center">
                  <Disc className="w-24 h-24 text-white/70" />
                </div>
                
                {/* Content */}
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <h3 className="font-futuristic text-xl font-bold text-dark-text">
                      {release.title}
                    </h3>
                    <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                      release.status === 'Available' 
                        ? 'bg-green-900/20 text-green-400 border border-green-500/30'
                        : 'bg-yellow-900/20 text-yellow-400 border border-yellow-500/30'
                    }`}>
                      {release.status}
                    </div>
                  </div>
                  
                  <p className="text-dark-muted mb-4 leading-relaxed">
                    {release.description}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="text-2xl font-bold text-neon-pink">
                        {release.price}
                      </span>
                      <span className="text-dark-muted text-sm ml-2">
                        via {release.platform}
                      </span>
                    </div>
                    
                    <a
                      href={release.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center space-x-2 px-4 py-2 bg-neon-pink hover:bg-neon-pink/80 text-black font-medium rounded-md transition-colors duration-300"
                    >
                      <ShoppingCart className="w-4 h-4" />
                      <span>{release.status === 'Available' ? 'Buy Now' : 'Pre-order'}</span>
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Digital Bundles */}
        <section className="mb-20">
          <div className="flex items-center space-x-3 mb-8">
            <Package className="w-8 h-8 text-neon-blue" />
            <h2 className="font-futuristic text-3xl font-bold text-neon-blue">
              Digital Bundles
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {digitalBundles.map((bundle) => (
              <div
                key={bundle.id}
                className="bg-dark-surface border border-dark-border rounded-lg p-6 hover:border-neon-blue/50 transition-all duration-300"
              >
                <h3 className="font-futuristic text-xl font-bold text-dark-text mb-3">
                  {bundle.title}
                </h3>
                
                <p className="text-dark-muted mb-4 leading-relaxed">
                  {bundle.description}
                </p>
                
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-xl font-bold text-neon-blue">
                      {bundle.price}
                    </span>
                    <span className="text-dark-muted text-sm ml-2">
                      via {bundle.platform}
                    </span>
                  </div>
                  
                  <a
                    href={bundle.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-2 px-4 py-2 bg-neon-blue hover:bg-neon-blue/80 text-black font-medium rounded-md transition-colors duration-300"
                  >
                    <ShoppingCart className="w-4 h-4" />
                    <span>Buy Now</span>
                    <ExternalLink className="w-3 h-3" />
                  </a>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Coming Soon */}
        <section className="text-center">
          <div className="bg-dark-surface border border-dark-border rounded-lg p-12">
            <h3 className="font-futuristic text-2xl font-bold mb-4 neon-text">
              More Artifacts Coming Soon
            </h3>
            <p className="text-dark-muted mb-8 max-w-2xl mx-auto">
              We&apos;re working on exclusive merchandise including apparel, art prints, 
              and limited edition physical releases. Subscribe to be notified when new items drop.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/subscribe"
                className="btn-neon inline-flex items-center space-x-2"
              >
                <span>Get Notified</span>
              </a>
              <a
                href="/music"
                className="px-6 py-3 bg-dark-border text-dark-text border border-dark-border rounded-md hover:bg-dark-muted/20 transition-colors duration-300"
              >
                Explore Music
              </a>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}
