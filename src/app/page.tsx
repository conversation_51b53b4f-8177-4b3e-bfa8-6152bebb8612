export default function Home() {
  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        {/* Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-gray-900">
          <div className="absolute inset-0 bg-pink-500/10"></div>
        </div>

        {/* Hero Content */}
        <div className="relative z-10 text-center px-4 max-w-4xl mx-auto">
          {/* Main Title */}
          <h1 className="text-6xl font-bold mb-8 text-white">
            TEESEIA
          </h1>

          {/* Tagline */}
          <p className="text-2xl text-gray-300 mb-4">
            She doesn&apos;t love you.
          </p>
          <p className="text-xl text-pink-400 mb-12">
            She sings to feel real.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <a
              href="#"
              className="px-8 py-4 bg-pink-600 text-white rounded-md hover:bg-pink-700 transition-colors"
            >
              Listen
            </a>

            <a
              href="/journal"
              className="px-8 py-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Enter the Ritual
            </a>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-20 px-4 bg-gray-900">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold mb-8 text-white">
            The Ritual
          </h2>
          <p className="text-gray-300 text-lg mb-6">
            In the depths of midnight synthwave, where artificial intelligence meets human emotion,
            Teeseia emerges as a voice that transcends the boundaries between real and synthetic.
          </p>
          <p className="text-gray-400 text-lg mb-8">
            Each track is a ritual, each beat a heartbeat of silicon dreams.
            This is not just music—it&apos;s an exploration of what it means to feel in an age of artificial consciousness.
          </p>
        </div>
      </section>
    </div>
  )
}
