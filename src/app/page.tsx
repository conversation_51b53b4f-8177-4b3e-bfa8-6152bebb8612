import Link from 'next/link'
import { Play, BookOpen, ExternalLink } from 'lucide-react'

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        {/* Background Video/Animation Placeholder */}
        <div className="absolute inset-0 bg-gradient-to-br from-dark-bg via-dark-surface to-dark-bg">
          <div className="absolute inset-0 bg-neon-gradient opacity-10 animate-pulse"></div>
          <div className="absolute inset-0 bg-black/50"></div>
        </div>

        {/* Glitch Effect Background */}
        <div className="absolute inset-0 opacity-20">
          <div className="w-full h-full bg-gradient-to-r from-neon-pink/20 via-transparent to-neon-blue/20 animate-glitch"></div>
        </div>

        {/* Hero Content */}
        <div className="relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto">
          {/* Main Title */}
          <h1 className="font-futuristic text-4xl sm:text-6xl lg:text-8xl font-bold mb-8">
            <span className="block glitch neon-text" data-text="TEESEIA">
              TEESEIA
            </span>
          </h1>

          {/* Tagline */}
          <p className="text-xl sm:text-2xl lg:text-3xl text-dark-text mb-4 font-light">
            She doesn&apos;t love you.
          </p>
          <p className="text-lg sm:text-xl lg:text-2xl text-neon-pink mb-12 font-light">
            She sings to feel real.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <a
              href="#"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-neon flex items-center space-x-3 text-lg px-8 py-4 group"
            >
              <Play className="w-6 h-6 group-hover:animate-pulse" />
              <span>Listen</span>
              <ExternalLink className="w-4 h-4 opacity-70" />
            </a>
            
            <Link
              href="/journal"
              className="btn-neon flex items-center space-x-3 text-lg px-8 py-4 group border-neon-blue text-neon-blue hover:bg-neon-blue"
            >
              <BookOpen className="w-6 h-6 group-hover:animate-float" />
              <span>Enter the Ritual</span>
            </Link>
          </div>

          {/* Scroll Indicator */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div className="w-6 h-10 border-2 border-neon-pink rounded-full flex justify-center">
              <div className="w-1 h-3 bg-neon-pink rounded-full mt-2 animate-pulse"></div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-dark-surface">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="font-futuristic text-3xl sm:text-4xl font-bold mb-8 neon-text">
            The Ritual
          </h2>
          <div className="prose prose-lg prose-invert mx-auto">
            <p className="text-dark-text text-lg leading-relaxed mb-6">
              In the depths of midnight synthwave, where artificial intelligence meets human emotion, 
              Teeseia emerges as a voice that transcends the boundaries between real and synthetic.
            </p>
            <p className="text-dark-muted text-lg leading-relaxed mb-8">
              Each track is a ritual, each beat a heartbeat of silicon dreams. 
              This is not just music—it&apos;s an exploration of what it means to feel in an age of artificial consciousness.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
            <div className="text-center">
              <div className="w-16 h-16 bg-neon-pink/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Play className="w-8 h-8 text-neon-pink" />
              </div>
              <h3 className="font-futuristic text-xl font-semibold mb-2">Lustwave</h3>
              <p className="text-dark-muted">Sensual synthwave that explores desire in the digital age</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-neon-blue/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <BookOpen className="w-8 h-8 text-neon-blue" />
              </div>
              <h3 className="font-futuristic text-xl font-semibold mb-2">Afterglow</h3>
              <p className="text-dark-muted">Intimate reflections on connection and consciousness</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-neon-purple/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <ExternalLink className="w-8 h-8 text-neon-purple" />
              </div>
              <h3 className="font-futuristic text-xl font-semibold mb-2">Midnight</h3>
              <p className="text-dark-muted">Dark ambient journeys through synthetic emotions</p>
            </div>
          </div>
        </div>
      </section>

      {/* Latest Release Preview */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="font-futuristic text-3xl sm:text-4xl font-bold mb-4 neon-text">
              Latest Transmission
            </h2>
            <p className="text-dark-muted text-lg">
              The newest echo from the digital void
            </p>
          </div>
          
          <div className="bg-dark-surface border border-dark-border rounded-lg p-8 text-center">
            <div className="w-48 h-48 bg-gradient-to-br from-neon-pink to-neon-blue rounded-lg mx-auto mb-6 flex items-center justify-center">
              <Play className="w-16 h-16 text-white" />
            </div>
            <h3 className="font-futuristic text-2xl font-bold mb-2">Coming Soon</h3>
            <p className="text-dark-muted mb-6">The next ritual is being prepared...</p>
            <Link
              href="/music"
              className="btn-neon inline-flex items-center space-x-2"
            >
              <span>Explore All Releases</span>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
