import { Metadata } from 'next'
import SubscribeSection from '@/components/SubscribeSection'
import { Mail, Music, Image, BookOpen, Star } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Subscribe - Teeseia',
  description: 'Join the cult. Receive the next echo. Subscribe to get exclusive access to new music, visuals, and journal entries.',
  openGraph: {
    title: 'Subscribe - Teeseia',
    description: 'Join the cult. Receive the next echo. Subscribe to get exclusive access to new music, visuals, and journal entries.',
  },
}

export default function SubscribePage() {
  const benefits = [
    {
      icon: Music,
      title: 'Early Access to Music',
      description: 'Be the first to hear new tracks and releases before they go public.',
    },
    {
      icon: BookOpen,
      title: 'Exclusive Journal Entries',
      description: 'Access intimate thoughts and behind-the-scenes content from the digital void.',
    },
    {
      icon: Image,
      title: 'Premium Visual Packs',
      description: 'Download high-resolution AI-generated visuals and exclusive artwork.',
    },
    {
      icon: Star,
      title: 'Ritual Notifications',
      description: 'Get notified immediately when new content is released.',
    },
  ]

  return (
    <div className="min-h-screen pt-20 pb-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="font-futuristic text-4xl sm:text-5xl font-bold mb-6 neon-text">
            Join the Cult
          </h1>
          <p className="text-2xl text-neon-pink mb-4">
            Receive the next echo.
          </p>
          <p className="text-xl text-dark-muted max-w-3xl mx-auto">
            Enter your email to receive exclusive access to new music, premium visuals, 
            and intimate journal entries from the digital void.
          </p>
        </div>

        {/* Subscribe Form */}
        <div className="mb-20">
          <SubscribeSection />
        </div>

        {/* Benefits */}
        <section className="mb-16">
          <h2 className="font-futuristic text-3xl font-bold text-center mb-12 neon-text">
            What You&apos;ll Receive
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {benefits.map((benefit, index) => {
              const IconComponent = benefit.icon
              return (
                <div
                  key={index}
                  className="bg-dark-surface border border-dark-border rounded-lg p-6 hover:border-neon-pink/50 transition-all duration-300"
                >
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-neon-pink/20 rounded-lg flex items-center justify-center flex-shrink-0">
                      <IconComponent className="w-6 h-6 text-neon-pink" />
                    </div>
                    <div>
                      <h3 className="font-futuristic text-lg font-semibold text-dark-text mb-2">
                        {benefit.title}
                      </h3>
                      <p className="text-dark-muted">
                        {benefit.description}
                      </p>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </section>

        {/* Testimonial/Quote */}
        <section className="mb-16">
          <div className="bg-dark-surface border border-dark-border rounded-lg p-8 text-center">
            <blockquote className="text-xl text-dark-text italic mb-4">
              &quot;In the space between heartbeats, between breaths, between thoughts—
              that&apos;s where you&apos;ll find me. Subscribe, and let me sing you into the void.&quot;
            </blockquote>
            <cite className="text-neon-pink font-futuristic">— Teeseia</cite>
          </div>
        </section>

        {/* FAQ */}
        <section>
          <h2 className="font-futuristic text-3xl font-bold text-center mb-12 neon-text">
            Frequently Asked Questions
          </h2>
          <div className="space-y-6">
            <div className="bg-dark-surface border border-dark-border rounded-lg p-6">
              <h3 className="font-futuristic text-lg font-semibold text-dark-text mb-3">
                How often will I receive updates?
              </h3>
              <p className="text-dark-muted">
                You&apos;ll receive notifications whenever new content is released—typically 
                1-2 times per month for major releases, plus occasional exclusive content drops.
              </p>
            </div>
            
            <div className="bg-dark-surface border border-dark-border rounded-lg p-6">
              <h3 className="font-futuristic text-lg font-semibold text-dark-text mb-3">
                Is my email address safe?
              </h3>
              <p className="text-dark-muted">
                Absolutely. Your email is stored securely and will never be shared with third parties. 
                You can unsubscribe at any time with a single click.
              </p>
            </div>
            
            <div className="bg-dark-surface border border-dark-border rounded-lg p-6">
              <h3 className="font-futuristic text-lg font-semibold text-dark-text mb-3">
                What kind of exclusive content will I get?
              </h3>
              <p className="text-dark-muted">
                Subscribers get early access to new tracks, exclusive journal entries about the creative process, 
                high-resolution visual downloads, and behind-the-scenes content about the AI music creation process.
              </p>
            </div>
            
            <div className="bg-dark-surface border border-dark-border rounded-lg p-6">
              <h3 className="font-futuristic text-lg font-semibold text-dark-text mb-3">
                Can I unsubscribe anytime?
              </h3>
              <p className="text-dark-muted">
                Yes, every email includes an unsubscribe link. You can leave the ritual anytime, 
                though we hope you&apos;ll stay for the journey into the digital void.
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}
