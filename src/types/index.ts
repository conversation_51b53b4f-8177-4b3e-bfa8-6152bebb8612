export interface JournalPost {
  id: string;
  title: string;
  content: string;
  mood: string;
  coverImage?: string;
  embedLinks?: {
    spotify?: string;
    youtube?: string;
    soundcloud?: string;
  };
  createdAt: Date;
  updatedAt: Date;
  featured?: boolean;
}

export interface MusicRelease {
  id: string;
  title: string;
  description: string;
  coverImage: string;
  releaseDate: Date;
  embedLinks: {
    spotify?: string;
    youtube?: string;
    bandcamp?: string;
    toneden?: string;
  };
  purchaseLinks?: {
    vinyl?: string;
    digital?: string;
  };
  featured?: boolean;
}

export interface Visual {
  id: string;
  title: string;
  imageUrl: string;
  thumbnailUrl?: string;
  description?: string;
  tags: string[];
  isPremium?: boolean;
  downloadUrl?: string;
  createdAt: Date;
}

export interface Subscriber {
  id: string;
  email: string;
  subscribedAt: Date;
  preferences?: {
    journalUpdates: boolean;
    musicReleases: boolean;
    visualUpdates: boolean;
  };
}

export type MoodTag = 
  | 'Lustwave'
  | 'Afterglow'
  | 'Midnight'
  | 'Synthwave'
  | 'Erotic'
  | 'Futuristic'
  | 'Ritual'
  | 'Neon';
