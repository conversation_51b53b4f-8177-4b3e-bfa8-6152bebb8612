'use client'

import { useEffect } from 'react'
import { X, Download, Lock, Calendar, Tag } from 'lucide-react'
import { Visual } from '@/types'

interface VisualModalProps {
  visual: Visual
  onClose: () => void
  onDownload: (visual: Visual) => void
}

const VisualModal = ({ visual, onClose, onDownload }: VisualModalProps) => {
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    document.addEventListener('keydown', handleEscape)
    document.body.style.overflow = 'hidden'

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [onClose])

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/80 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative bg-dark-surface border border-dark-border rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-dark-border">
          <div className="flex items-center space-x-4">
            <h2 className="font-futuristic text-2xl font-bold text-dark-text">
              {visual.title}
            </h2>
            {visual.isPremium && (
              <div className="flex items-center space-x-1 px-3 py-1 bg-neon-purple/20 border border-neon-purple rounded-full">
                <Lock className="w-4 h-4 text-neon-purple" />
                <span className="text-neon-purple text-sm font-medium">Premium</span>
              </div>
            )}
          </div>
          
          <button
            onClick={onClose}
            className="w-10 h-10 flex items-center justify-center text-dark-muted hover:text-dark-text hover:bg-dark-bg rounded-full transition-colors duration-300"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="flex flex-col lg:flex-row max-h-[calc(90vh-80px)]">
          {/* Image */}
          <div className="flex-1 bg-dark-bg flex items-center justify-center p-6">
            <div className="w-full max-w-lg aspect-square bg-gradient-to-br from-neon-pink/30 to-neon-blue/30 rounded-lg flex items-center justify-center">
              <span className="text-white/70 text-lg">Visual Placeholder</span>
            </div>
          </div>

          {/* Details */}
          <div className="w-full lg:w-80 p-6 border-t lg:border-t-0 lg:border-l border-dark-border overflow-y-auto">
            {/* Description */}
            <div className="mb-6">
              <h3 className="font-futuristic text-lg font-semibold text-dark-text mb-3">
                Description
              </h3>
              <p className="text-dark-muted leading-relaxed">
                {visual.description}
              </p>
            </div>

            {/* Metadata */}
            <div className="mb-6">
              <h3 className="font-futuristic text-lg font-semibold text-dark-text mb-3">
                Details
              </h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3 text-sm">
                  <Calendar className="w-4 h-4 text-dark-muted" />
                  <span className="text-dark-muted">Created:</span>
                  <span className="text-dark-text">{formatDate(visual.createdAt)}</span>
                </div>
                <div className="flex items-center space-x-3 text-sm">
                  <Tag className="w-4 h-4 text-dark-muted" />
                  <span className="text-dark-muted">Type:</span>
                  <span className="text-dark-text">
                    {visual.isPremium ? 'Premium' : 'Free'}
                  </span>
                </div>
              </div>
            </div>

            {/* Tags */}
            <div className="mb-6">
              <h3 className="font-futuristic text-lg font-semibold text-dark-text mb-3">
                Tags
              </h3>
              <div className="flex flex-wrap gap-2">
                {visual.tags.map((tag) => (
                  <span
                    key={tag}
                    className="px-3 py-1 bg-dark-bg border border-dark-border text-dark-text text-sm rounded-full"
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            </div>

            {/* Download Section */}
            <div className="border-t border-dark-border pt-6">
              <h3 className="font-futuristic text-lg font-semibold text-dark-text mb-3">
                Download
              </h3>
              
              {visual.isPremium ? (
                <div className="space-y-4">
                  <p className="text-dark-muted text-sm">
                    This is premium content. Subscribe to access high-resolution downloads 
                    and exclusive visual packs.
                  </p>
                  <button
                    onClick={() => onDownload(visual)}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-neon-purple hover:bg-neon-purple/80 text-white rounded-md transition-colors duration-300"
                  >
                    <Lock className="w-5 h-5" />
                    <span>Subscribe for Access</span>
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  <p className="text-dark-muted text-sm">
                    Free download available. Click below to save this visual to your device.
                  </p>
                  <button
                    onClick={() => onDownload(visual)}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-neon-blue hover:bg-neon-blue/80 text-white rounded-md transition-colors duration-300"
                  >
                    <Download className="w-5 h-5" />
                    <span>Download Free</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default VisualModal
