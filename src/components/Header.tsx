'use client'

import Link from 'next/link'
import { useState } from 'react'
import { Menu, X, Music, BookOpen, Image, ShoppingBag, Mail } from 'lucide-react'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const navigation = [
    { name: 'Music', href: '/music', icon: Music },
    { name: 'Journal', href: '/journal', icon: BookOpen },
    { name: 'Visuals', href: '/visuals', icon: Image },
    { name: 'Merch', href: '/merch', icon: ShoppingBag },
    { name: 'Subscribe', href: '/subscribe', icon: Mail },
  ]

  return (
    <header className="fixed top-0 w-full z-50 bg-dark-bg/80 backdrop-blur-custom border-b border-dark-border">
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <span className="font-futuristic text-2xl font-bold neon-text">
              TEESEIA
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => {
              const IconComponent = item.icon
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className="flex items-center space-x-2 text-dark-text hover:text-neon-pink transition-colors duration-300 group"
                >
                  <IconComponent className="w-4 h-4 group-hover:text-neon-pink" />
                  <span className="font-medium">{item.name}</span>
                </Link>
              )
            })}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-dark-text hover:text-neon-pink transition-colors duration-300"
            >
              {isMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-dark-surface/95 backdrop-blur-custom rounded-lg mt-2 border border-dark-border">
              {navigation.map((item) => {
                const IconComponent = item.icon
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="flex items-center space-x-3 px-3 py-2 text-dark-text hover:text-neon-pink hover:bg-dark-bg/50 rounded-md transition-all duration-300"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <IconComponent className="w-5 h-5" />
                    <span className="font-medium">{item.name}</span>
                  </Link>
                )
              })}
            </div>
          </div>
        )}
      </nav>
    </header>
  )
}

export default Header
