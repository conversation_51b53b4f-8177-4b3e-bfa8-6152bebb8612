'use client'

import Image from 'next/image'
import { useState } from 'react'
import { Play, ExternalLink, ShoppingCart, Calendar } from 'lucide-react'
import { MusicRelease } from '@/types'

interface AlbumCardProps {
  release: MusicRelease
  featured?: boolean
}

const AlbumCard = ({ release, featured = false }: AlbumCardProps) => {
  const [showEmbeds, setShowEmbeds] = useState(false)

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  return (
    <div className={`bg-dark-surface border border-dark-border rounded-lg overflow-hidden transition-all duration-300 hover:border-neon-pink/50 hover:shadow-lg hover:shadow-neon-pink/20 ${featured ? 'lg:col-span-2' : ''}`}>
      {/* Album Cover */}
      <div className="relative aspect-square bg-gradient-to-br from-neon-pink/20 to-neon-blue/20 flex items-center justify-center">
        {/* Placeholder for album cover */}
        <div className="w-full h-full bg-gradient-to-br from-neon-pink/30 to-neon-blue/30 flex items-center justify-center">
          <Play className="w-16 h-16 text-white/70" />
        </div>
        
        {/* Play Overlay */}
        <div className="absolute inset-0 bg-black/50 opacity-0 hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
          <button
            onClick={() => setShowEmbeds(!showEmbeds)}
            className="w-16 h-16 bg-neon-pink rounded-full flex items-center justify-center hover:bg-neon-pink/80 transition-colors duration-300"
          >
            <Play className="w-8 h-8 text-white ml-1" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Title and Date */}
        <div className="mb-4">
          <h3 className="font-futuristic text-xl font-bold mb-2 text-dark-text">
            {release.title}
          </h3>
          <div className="flex items-center text-dark-muted text-sm">
            <Calendar className="w-4 h-4 mr-2" />
            <span>{formatDate(release.releaseDate)}</span>
          </div>
        </div>

        {/* Description */}
        <p className="text-dark-muted text-sm mb-6 leading-relaxed">
          {release.description}
        </p>

        {/* Embeds Section */}
        {showEmbeds && (
          <div className="mb-6 space-y-4">
            {release.embedLinks.spotify && (
              <div className="aspect-[4/1] bg-dark-bg rounded-lg flex items-center justify-center">
                <span className="text-dark-muted">Spotify Embed Placeholder</span>
              </div>
            )}
            {release.embedLinks.youtube && (
              <div className="aspect-video bg-dark-bg rounded-lg flex items-center justify-center">
                <span className="text-dark-muted">YouTube Embed Placeholder</span>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          {/* Streaming Links */}
          <div className="flex flex-wrap gap-2">
            {release.embedLinks.spotify && (
              <a
                href={release.embedLinks.spotify}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded-md transition-colors duration-300"
              >
                <Play className="w-4 h-4" />
                <span>Spotify</span>
                <ExternalLink className="w-3 h-3" />
              </a>
            )}
            
            {release.embedLinks.youtube && (
              <a
                href={release.embedLinks.youtube}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-md transition-colors duration-300"
              >
                <Play className="w-4 h-4" />
                <span>YouTube</span>
                <ExternalLink className="w-3 h-3" />
              </a>
            )}
            
            {release.embedLinks.toneden && (
              <a
                href={release.embedLinks.toneden}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-md transition-colors duration-300"
              >
                <ExternalLink className="w-4 h-4" />
                <span>All Platforms</span>
              </a>
            )}
          </div>

          {/* Purchase Links */}
          {(release.purchaseLinks?.vinyl || release.purchaseLinks?.digital) && (
            <div className="flex flex-wrap gap-2 pt-2 border-t border-dark-border">
              {release.purchaseLinks.vinyl && (
                <a
                  href={release.purchaseLinks.vinyl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-2 px-3 py-2 bg-neon-pink hover:bg-neon-pink/80 text-black text-sm rounded-md transition-colors duration-300 font-medium"
                >
                  <ShoppingCart className="w-4 h-4" />
                  <span>Vinyl</span>
                  <ExternalLink className="w-3 h-3" />
                </a>
              )}
              
              {release.purchaseLinks.digital && (
                <a
                  href={release.purchaseLinks.digital}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-2 px-3 py-2 bg-neon-blue hover:bg-neon-blue/80 text-black text-sm rounded-md transition-colors duration-300 font-medium"
                >
                  <ShoppingCart className="w-4 h-4" />
                  <span>Digital</span>
                  <ExternalLink className="w-3 h-3" />
                </a>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default AlbumCard
