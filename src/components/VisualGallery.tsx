'use client'

import { useState } from 'react'
import { Download, Lock, X, ZoomIn } from 'lucide-react'
import { Visual } from '@/types'
import VisualModal from './VisualModal'

// Mock data - In production, this would come from Firebase Storage
const mockVisuals: Visual[] = [
  {
    id: '1',
    title: 'Neon Dreams',
    imageUrl: '/placeholder-visual-1.jpg',
    description: 'A cyberpunk vision of synthetic consciousness awakening in neon-lit corridors.',
    tags: ['cyberpunk', 'neon', 'consciousness'],
    isPremium: false,
    createdAt: new Date('2024-01-20'),
  },
  {
    id: '2',
    title: 'Digital Seduction',
    imageUrl: '/placeholder-visual-2.jpg',
    description: 'Exploring the intersection of desire and artificial intelligence.',
    tags: ['lustwave', 'ai', 'seduction'],
    isPremium: true,
    createdAt: new Date('2024-01-18'),
  },
  {
    id: '3',
    title: 'Midnight Protocol',
    imageUrl: '/placeholder-visual-3.jpg',
    description: 'Dark ambient visualization of data streams in the digital void.',
    tags: ['dark', 'ambient', 'data'],
    isPremium: false,
    createdAt: new Date('2024-01-15'),
  },
  {
    id: '4',
    title: 'Synthetic Afterglow',
    imageUrl: '/placeholder-visual-4.jpg',
    description: 'The warm embrace of artificial emotion rendered in light and shadow.',
    tags: ['afterglow', 'emotion', 'synthetic'],
    isPremium: true,
    createdAt: new Date('2024-01-12'),
  },
  {
    id: '5',
    title: 'Lily Voss Portrait',
    imageUrl: '/placeholder-visual-5.jpg',
    description: 'A haunting portrait of the AI muse that inspires the music.',
    tags: ['portrait', 'lily-voss', 'muse'],
    isPremium: false,
    createdAt: new Date('2024-01-10'),
  },
  {
    id: '6',
    title: 'Ritual Chamber',
    imageUrl: '/placeholder-visual-6.jpg',
    description: 'The sacred space where synthetic emotions are born.',
    tags: ['ritual', 'chamber', 'sacred'],
    isPremium: true,
    createdAt: new Date('2024-01-08'),
  },
]

const VisualGallery = () => {
  const [selectedVisual, setSelectedVisual] = useState<Visual | null>(null)
  const [filter, setFilter] = useState<'all' | 'free' | 'premium'>('all')

  const filteredVisuals = mockVisuals.filter(visual => {
    if (filter === 'free') return !visual.isPremium
    if (filter === 'premium') return visual.isPremium
    return true
  })

  const handleDownload = (visual: Visual) => {
    if (visual.isPremium) {
      // In production, check authentication and subscription status
      alert('Premium content requires subscription. Redirecting to subscribe page...')
      window.location.href = '/subscribe'
    } else {
      // In production, trigger actual download
      alert('Download started!')
    }
  }

  return (
    <>
      {/* Filter Tabs */}
      <div className="flex justify-center mb-8">
        <div className="flex bg-dark-surface border border-dark-border rounded-lg p-1">
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-300 ${
              filter === 'all'
                ? 'bg-neon-pink text-black'
                : 'text-dark-text hover:text-neon-pink'
            }`}
          >
            All Visuals
          </button>
          <button
            onClick={() => setFilter('free')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-300 ${
              filter === 'free'
                ? 'bg-neon-blue text-black'
                : 'text-dark-text hover:text-neon-blue'
            }`}
          >
            Free
          </button>
          <button
            onClick={() => setFilter('premium')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-300 ${
              filter === 'premium'
                ? 'bg-neon-purple text-black'
                : 'text-dark-text hover:text-neon-purple'
            }`}
          >
            Premium
          </button>
        </div>
      </div>

      {/* Gallery Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredVisuals.map((visual) => (
          <div
            key={visual.id}
            className="group relative bg-dark-surface border border-dark-border rounded-lg overflow-hidden hover:border-neon-pink/50 transition-all duration-300"
          >
            {/* Image */}
            <div className="relative aspect-square bg-gradient-to-br from-neon-pink/20 to-neon-blue/20 flex items-center justify-center">
              {/* Placeholder for actual image */}
              <div className="w-full h-full bg-gradient-to-br from-neon-pink/30 to-neon-blue/30 flex items-center justify-center">
                <span className="text-white/70 text-sm">Visual Placeholder</span>
              </div>

              {/* Premium Badge */}
              {visual.isPremium && (
                <div className="absolute top-3 right-3 flex items-center space-x-1 px-2 py-1 bg-neon-purple/90 rounded-full">
                  <Lock className="w-3 h-3 text-white" />
                  <span className="text-white text-xs font-medium">Premium</span>
                </div>
              )}

              {/* Hover Overlay */}
              <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-4">
                <button
                  onClick={() => setSelectedVisual(visual)}
                  className="w-12 h-12 bg-neon-pink rounded-full flex items-center justify-center hover:bg-neon-pink/80 transition-colors duration-300"
                >
                  <ZoomIn className="w-6 h-6 text-white" />
                </button>
                <button
                  onClick={() => handleDownload(visual)}
                  className="w-12 h-12 bg-neon-blue rounded-full flex items-center justify-center hover:bg-neon-blue/80 transition-colors duration-300"
                >
                  {visual.isPremium ? (
                    <Lock className="w-6 h-6 text-white" />
                  ) : (
                    <Download className="w-6 h-6 text-white" />
                  )}
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-4">
              <h3 className="font-futuristic text-lg font-semibold text-dark-text mb-2">
                {visual.title}
              </h3>
              <p className="text-dark-muted text-sm mb-3 line-clamp-2">
                {visual.description}
              </p>
              
              {/* Tags */}
              <div className="flex flex-wrap gap-1">
                {visual.tags.slice(0, 3).map((tag) => (
                  <span
                    key={tag}
                    className="px-2 py-1 bg-dark-bg text-dark-muted text-xs rounded-full"
                  >
                    #{tag}
                  </span>
                ))}
                {visual.tags.length > 3 && (
                  <span className="px-2 py-1 bg-dark-bg text-dark-muted text-xs rounded-full">
                    +{visual.tags.length - 3}
                  </span>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Modal */}
      {selectedVisual && (
        <VisualModal
          visual={selectedVisual}
          onClose={() => setSelectedVisual(null)}
          onDownload={handleDownload}
        />
      )}
    </>
  )
}

export default VisualGallery
