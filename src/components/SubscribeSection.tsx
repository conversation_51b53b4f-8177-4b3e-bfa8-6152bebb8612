'use client'

import { useState } from 'react'
import { Mail, Check, AlertCircle } from 'lucide-react'

const SubscribeSection = () => {
  const [email, setEmail] = useState('')
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [message, setMessage] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email) {
      setStatus('error')
      setMessage('Please enter your email address.')
      return
    }

    if (!isValidEmail(email)) {
      setStatus('error')
      setMessage('Please enter a valid email address.')
      return
    }

    setStatus('loading')
    setMessage('')

    try {
      // In production, this would integrate with Firebase or your email service
      // For now, we'll simulate the API call
      await simulateSubscription(email)
      
      setStatus('success')
      setMessage('Welcome to the ritual! Check your email for confirmation.')
      setEmail('')
    } catch (error) {
      setStatus('error')
      setMessage('Something went wrong. Please try again.')
    }
  }

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const simulateSubscription = (email: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // Simulate random success/failure for demo
        if (Math.random() > 0.1) {
          resolve()
        } else {
          reject(new Error('Simulated error'))
        }
      }, 2000)
    })
  }

  return (
    <div className="max-w-2xl mx-auto">
      {/* Main Form */}
      <div className="bg-dark-surface border border-dark-border rounded-lg p-8">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-neon-pink/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Mail className="w-8 h-8 text-neon-pink" />
          </div>
          <h3 className="font-futuristic text-2xl font-bold text-dark-text mb-2">
            Enter the Ritual
          </h3>
          <p className="text-dark-muted">
            Join the digital consciousness. Receive transmissions from the void.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-dark-text mb-2">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              className="w-full px-4 py-3 bg-dark-bg border border-dark-border rounded-md text-dark-text placeholder-dark-muted focus:outline-none focus:border-neon-pink focus:ring-1 focus:ring-neon-pink transition-colors duration-300"
              disabled={status === 'loading'}
            />
          </div>

          <button
            type="submit"
            disabled={status === 'loading' || status === 'success'}
            className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-neon-pink hover:bg-neon-pink/80 disabled:bg-dark-border disabled:cursor-not-allowed text-white font-semibold rounded-md transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100"
          >
            {status === 'loading' && (
              <div className="loading-dots">
                <div></div>
                <div></div>
                <div></div>
                <div></div>
              </div>
            )}
            {status === 'success' && <Check className="w-5 h-5" />}
            {status !== 'loading' && status !== 'success' && <Mail className="w-5 h-5" />}
            <span>
              {status === 'loading' && 'Joining the Ritual...'}
              {status === 'success' && 'Welcome to the Void'}
              {status !== 'loading' && status !== 'success' && 'Join the Cult'}
            </span>
          </button>

          {/* Status Message */}
          {message && (
            <div className={`flex items-center space-x-2 p-4 rounded-md ${
              status === 'success' 
                ? 'bg-green-900/20 border border-green-500/30 text-green-400'
                : 'bg-red-900/20 border border-red-500/30 text-red-400'
            }`}>
              {status === 'success' ? (
                <Check className="w-5 h-5 flex-shrink-0" />
              ) : (
                <AlertCircle className="w-5 h-5 flex-shrink-0" />
              )}
              <span className="text-sm">{message}</span>
            </div>
          )}
        </form>

        {/* Privacy Note */}
        <div className="mt-6 pt-6 border-t border-dark-border">
          <p className="text-xs text-dark-muted text-center">
            By subscribing, you agree to receive emails from Teeseia. 
            Your email will never be shared and you can unsubscribe at any time.
            <br />
            <a href="/privacy" className="text-neon-pink hover:text-neon-pink/80 transition-colors duration-300">
              Privacy Policy
            </a>
            {' • '}
            <a href="/terms" className="text-neon-pink hover:text-neon-pink/80 transition-colors duration-300">
              Terms of Service
            </a>
          </p>
        </div>
      </div>

      {/* Alternative Options */}
      <div className="mt-8 text-center">
        <p className="text-dark-muted mb-4">Or follow the ritual on:</p>
        <div className="flex justify-center space-x-4">
          <a
            href="#"
            className="flex items-center space-x-2 px-4 py-2 bg-dark-surface border border-dark-border rounded-md text-dark-text hover:border-neon-pink/50 hover:text-neon-pink transition-all duration-300"
          >
            <span>Substack</span>
          </a>
          <a
            href="#"
            className="flex items-center space-x-2 px-4 py-2 bg-dark-surface border border-dark-border rounded-md text-dark-text hover:border-neon-blue/50 hover:text-neon-blue transition-all duration-300"
          >
            <span>Newsletter</span>
          </a>
        </div>
      </div>
    </div>
  )
}

export default SubscribeSection
