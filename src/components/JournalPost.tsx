'use client'

import { useState } from 'react'
import { Calendar, Tag, Play, ExternalLink } from 'lucide-react'
import { JournalPost as JournalPostType, MoodTag } from '@/types'

interface JournalPostProps {
  post: JournalPostType
  featured?: boolean
}

const JournalPost = ({ post, featured = false }: JournalPostProps) => {
  const [showEmbeds, setShowEmbeds] = useState(false)
  const [isExpanded, setIsExpanded] = useState(featured)

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const getMoodColor = (mood: string): string => {
    const moodColors: Record<string, string> = {
      'Lustwave': 'text-neon-pink border-neon-pink',
      'Afterglow': 'text-neon-blue border-neon-blue',
      'Midnight': 'text-neon-purple border-neon-purple',
      'Synthwave': 'text-cyan-400 border-cyan-400',
      'Erotic': 'text-red-400 border-red-400',
      'Futuristic': 'text-green-400 border-green-400',
      'Ritual': 'text-yellow-400 border-yellow-400',
      'Neon': 'text-pink-400 border-pink-400',
    }
    return moodColors[mood] || 'text-gray-400 border-gray-400'
  }

  const getPreviewText = (content: string, maxLength: number = 200): string => {
    if (content.length <= maxLength) return content
    return content.substring(0, maxLength).trim() + '...'
  }

  return (
    <article className={`bg-dark-surface border border-dark-border rounded-lg overflow-hidden transition-all duration-300 hover:border-neon-pink/30 ${featured ? 'border-neon-pink/50' : ''}`}>
      {/* Header */}
      <div className="p-6 border-b border-dark-border">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h2 className="font-futuristic text-xl sm:text-2xl font-bold text-dark-text mb-2">
              {post.title}
            </h2>
            <div className="flex items-center space-x-4 text-sm text-dark-muted">
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4" />
                <span>{formatDate(post.createdAt)}</span>
              </div>
              <div className={`flex items-center space-x-2 px-2 py-1 border rounded-full ${getMoodColor(post.mood)}`}>
                <Tag className="w-3 h-3" />
                <span className="text-xs font-medium">{post.mood}</span>
              </div>
            </div>
          </div>
          
          {featured && (
            <div className="flex items-center space-x-2 px-3 py-1 bg-neon-pink/20 border border-neon-pink rounded-full">
              <span className="text-neon-pink text-xs font-medium uppercase tracking-wider">
                Featured
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="prose prose-invert max-w-none">
          <div className="text-dark-text leading-relaxed whitespace-pre-line">
            {isExpanded ? post.content : getPreviewText(post.content)}
          </div>
          
          {!isExpanded && post.content.length > 200 && (
            <button
              onClick={() => setIsExpanded(true)}
              className="mt-4 text-neon-pink hover:text-neon-pink/80 transition-colors duration-300 font-medium"
            >
              Read more →
            </button>
          )}
          
          {isExpanded && !featured && (
            <button
              onClick={() => setIsExpanded(false)}
              className="mt-4 text-dark-muted hover:text-dark-text transition-colors duration-300"
            >
              ← Show less
            </button>
          )}
        </div>

        {/* Embeds Section */}
        {post.embedLinks && Object.keys(post.embedLinks).length > 0 && (
          <div className="mt-6 pt-6 border-t border-dark-border">
            <button
              onClick={() => setShowEmbeds(!showEmbeds)}
              className="flex items-center space-x-2 text-neon-blue hover:text-neon-blue/80 transition-colors duration-300 mb-4"
            >
              <Play className="w-4 h-4" />
              <span className="font-medium">
                {showEmbeds ? 'Hide' : 'Show'} Associated Tracks
              </span>
            </button>

            {showEmbeds && (
              <div className="space-y-4">
                {post.embedLinks.spotify && (
                  <div className="aspect-[4/1] bg-dark-bg rounded-lg flex items-center justify-center border border-dark-border">
                    <span className="text-dark-muted">Spotify Embed Placeholder</span>
                  </div>
                )}
                {post.embedLinks.youtube && (
                  <div className="aspect-video bg-dark-bg rounded-lg flex items-center justify-center border border-dark-border">
                    <span className="text-dark-muted">YouTube Embed Placeholder</span>
                  </div>
                )}
                {post.embedLinks.soundcloud && (
                  <div className="aspect-[4/1] bg-dark-bg rounded-lg flex items-center justify-center border border-dark-border">
                    <span className="text-dark-muted">SoundCloud Embed Placeholder</span>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* External Links */}
        {post.embedLinks && (
          <div className="mt-6 flex flex-wrap gap-2">
            {post.embedLinks.spotify && (
              <a
                href={post.embedLinks.spotify}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded-md transition-colors duration-300"
              >
                <Play className="w-4 h-4" />
                <span>Spotify</span>
                <ExternalLink className="w-3 h-3" />
              </a>
            )}
            
            {post.embedLinks.youtube && (
              <a
                href={post.embedLinks.youtube}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-md transition-colors duration-300"
              >
                <Play className="w-4 h-4" />
                <span>YouTube</span>
                <ExternalLink className="w-3 h-3" />
              </a>
            )}
            
            {post.embedLinks.soundcloud && (
              <a
                href={post.embedLinks.soundcloud}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 px-3 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm rounded-md transition-colors duration-300"
              >
                <Play className="w-4 h-4" />
                <span>SoundCloud</span>
                <ExternalLink className="w-3 h-3" />
              </a>
            )}
          </div>
        )}
      </div>
    </article>
  )
}

export default JournalPost
