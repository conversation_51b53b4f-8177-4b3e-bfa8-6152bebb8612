import Link from 'next/link'
import { ExternalLink, Instagram, Twitter, Youtube, Music } from 'lucide-react'

const Footer = () => {
  const currentYear = new Date().getFullYear()

  const socialLinks = [
    {
      name: 'Spotify',
      href: '#',
      icon: Music,
    },
    {
      name: 'YouTube',
      href: '#',
      icon: Youtube,
    },
    {
      name: 'Instagram',
      href: '#',
      icon: Instagram,
    },
    {
      name: 'Twitter',
      href: '#',
      icon: Twitter,
    },
  ]

  const footerLinks = [
    {
      title: 'Music',
      links: [
        { name: 'Latest Releases', href: '/music' },
        { name: 'Spotify', href: '#' },
        { name: 'Bandcamp', href: '#' },
        { name: 'ToneDen', href: '#' },
      ],
    },
    {
      title: 'Content',
      links: [
        { name: 'Journal', href: '/journal' },
        { name: 'Visuals', href: '/visuals' },
        { name: 'Behind the Ritual', href: '/journal' },
      ],
    },
    {
      title: 'Connect',
      links: [
        { name: 'Subscribe', href: '/subscribe' },
        { name: 'Merch', href: '/merch' },
        { name: 'Contact', href: '/subscribe' },
      ],
    },
  ]

  return (
    <footer className="bg-dark-surface border-t border-dark-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="col-span-1 md:col-span-1">
            <Link href="/" className="flex items-center space-x-2 mb-4">
              <span className="font-futuristic text-xl font-bold neon-text">
                TEESEIA
              </span>
            </Link>
            <p className="text-dark-muted text-sm mb-6 max-w-xs">
              She doesn&apos;t love you. She sings to feel real. Enter the ritual of AI synthwave.
            </p>
            
            {/* Social Links */}
            <div className="flex space-x-4">
              {socialLinks.map((social) => {
                const IconComponent = social.icon
                return (
                  <a
                    key={social.name}
                    href={social.href}
                    className="text-dark-muted hover:text-neon-pink transition-colors duration-300"
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label={social.name}
                  >
                    <IconComponent className="w-5 h-5" />
                  </a>
                )
              })}
            </div>
          </div>

          {/* Footer Links */}
          {footerLinks.map((section) => (
            <div key={section.title} className="col-span-1">
              <h3 className="font-futuristic text-sm font-semibold text-dark-text uppercase tracking-wider mb-4">
                {section.title}
              </h3>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-dark-muted hover:text-neon-pink transition-colors duration-300 text-sm flex items-center group"
                    >
                      {link.name}
                      {link.href.startsWith('http') && (
                        <ExternalLink className="w-3 h-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      )}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom Section */}
        <div className="mt-12 pt-8 border-t border-dark-border">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-dark-muted text-sm">
              © {currentYear} Teeseia. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link
                href="/privacy"
                className="text-dark-muted hover:text-neon-pink transition-colors duration-300 text-sm"
              >
                Privacy Policy
              </Link>
              <Link
                href="/terms"
                className="text-dark-muted hover:text-neon-pink transition-colors duration-300 text-sm"
              >
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
