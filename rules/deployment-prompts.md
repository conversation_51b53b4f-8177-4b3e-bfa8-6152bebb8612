# Next.js + Firebase Deployment Prompts

## Netlify Deployment Setup

```prompt
Create a complete Netlify deployment configuration for my Next.js + Firebase project including:
- netlify.toml file with build settings
- Next.js plugin configuration
- Environment variables setup
- Redirects and headers configuration
- Build optimization settings
- Branch deployment strategy
```

## Performance Optimization

```prompt
Optimize my Next.js + Firebase deployment for performance by:
- Configuring next.config.js for production
- Setting up code splitting and lazy loading
- Implementing Firebase SDK modular imports
- Configuring image optimization
- Setting up caching strategies
- Minimizing JavaScript bundle size
```

## CI/CD Pipeline

```prompt
Design a CI/CD pipeline for my Next.js + Firebase project that:
- Automates testing before deployment
- Implements staging and production environments
- Sets up preview deployments for pull requests
- Configures automatic Firebase function deployment
- Implements build caching for faster deployments
- Adds post-deployment verification checks
```

## Security Configuration

```prompt
Create a security configuration for my Next.js + Firebase deployment that:
- Sets up proper Content Security Policy headers
- Configures Firebase security rules
- Implements authentication protection
- Sets up rate limiting for API routes
- Configures proper CORS settings
- Implements secure cookie handling
```

## Multi-Environment Setup

```prompt
Design a multi-environment setup for my Next.js + Firebase project with:
- Development, staging, and production configurations
- Environment-specific Firebase projects
- Proper environment variable management
- Branch-based deployment strategy
- Database isolation between environments
- Testing strategy across environments
```

## Custom Domain & SSL

```prompt
Configure custom domain and SSL for my Next.js + Firebase project:
- DNS configuration for Netlify
- SSL certificate setup
- Firebase custom domain configuration
- Domain verification process
- Redirect configuration (www vs non-www)
- Force HTTPS implementation
```

## Monitoring & Analytics

```prompt
Set up monitoring and analytics for my deployed Next.js + Firebase application:
- Error tracking configuration
- Performance monitoring setup
- User analytics implementation
- Server-side analytics with Netlify
- Firebase Performance Monitoring
- Real-time alerting for critical issues
```

## Serverless Functions

```prompt
Create a serverless functions strategy for my Next.js + Firebase project:
- Next.js API routes implementation
- Firebase Cloud Functions integration
- Netlify Functions configuration
- Cold start optimization
- Function bundling and optimization
- Local development environment for functions
```

## Deployment Automation

```prompt
Create an automated deployment workflow that:
- Triggers deployments on git push
- Runs pre-deployment tests and checks
- Updates Firebase security rules
- Deploys Firebase functions
- Builds and deploys Next.js application
- Performs post-deployment verification
```

## Backup & Disaster Recovery

```prompt
Design a backup and disaster recovery plan for my Next.js + Firebase application:
- Firebase database backup strategy
- Deployment rollback procedures
- Data recovery process
- High availability configuration
- Incident response workflow
- Regular backup testing procedure
```

## Cost Optimization

```prompt
Optimize hosting and infrastructure costs for my Next.js + Firebase application:
- Netlify plan recommendations
- Firebase usage optimization
- Serverless function execution optimization
- Image and asset delivery optimization
- Database usage optimization
- Caching strategy to reduce costs
```

## Alternative Hosting Options

```prompt
Compare hosting options for my Next.js + Firebase application:
- Netlify vs Vercel detailed comparison
- Firebase Hosting configuration
- AWS Amplify setup
- Google Cloud Run deployment
- Digital Ocean App Platform configuration
- Pros and cons of each platform for my specific needs
```