# SEO Optimization Prompts for Next.js & Firebase Projects

## Core Prompts from FlexiBlog Success Patterns

```prompt
Create a centralized SEO configuration similar to FlexiBlog that:
- Defines site metadata in a central config file
- Includes site title, description, author info, and social links
- Configures default images for social sharing
- Sets up site URL and language preferences
- Defines content categories and taxonomies for better organization
```

```prompt
Design a content structure for [PROJECT_TYPE] with these FlexiBlog-inspired features:
- Frontmatter metadata for each content piece (title, date, author, category, tags)
- Featured content flagging system
- Thumbnail and featured image handling
- Related content suggestions based on categories and tags
- Reading time calculation
```

```prompt
Create a tagging and categorization system that:
- Organizes content hierarchically
- Generates SEO-friendly category and tag pages
- Implements proper breadcrumb navigation with structured data
- Creates topic clusters for improved topical authority
- Adds Schema.org markup for content relationships
```

## Page Templates & Layouts

```prompt
Design a blog post template with these SEO features:
- Semantic HTML structure (article, section, header, etc.)
- Schema.org Article markup with all required properties
- Social sharing metadata customized per post
- Related articles section for internal linking
- Author information with Schema.org Person markup
- Optimized heading structure (H1, H2, H3)
```

```prompt
Create a category page template that:
- Implements proper canonical URLs
- Uses pagination with rel="next" and rel="prev"
- Includes category description with targeted keywords
- Shows featured posts prominently
- Adds breadcrumb navigation with structured data
```

## Rich Media Handling

```prompt
Develop a media optimization strategy that:
- Implements responsive images with srcset
- Adds structured data for images and videos
- Creates optimized image components with lazy loading
- Handles embedded videos with SEO-friendly wrappers
- Implements image compression and WebP conversion
```

```prompt
Create a video embedding component that:
- Loads YouTube/Vimeo videos with minimal performance impact
- Adds proper Schema.org VideoObject markup
- Implements video thumbnails for faster page loading
- Provides video transcripts for accessibility and SEO
- Tracks video engagement metrics
```

## Content Strategy Prompts

```prompt
Develop a content strategy based on FlexiBlog patterns that:
- Creates pillar content and supporting articles
- Implements internal linking structure
- Balances keyword optimization with readability
- Schedules content updates for freshness signals
- Leverages categories and tags for topic clusters
```

```prompt
Generate a markdown content template for [CONTENT_TYPE] with:
- Frontmatter metadata section with all required SEO fields
- Structured heading outline (H2, H3, H4)
- Placeholder sections for key content components
- Image placement with alt text templates
- Internal and external linking opportunities
- FAQ section with Schema.org markup
```

## Technical SEO Enhancements

```prompt
Create a performance optimization strategy that:
- Implements code splitting and lazy loading
- Optimizes Core Web Vitals (LCP, FID, CLS)
- Minimizes JavaScript bundle size
- Implements proper caching strategies
- Optimizes Firebase data fetching for performance
```

```prompt
Develop an internationalization strategy that:
- Implements hreflang tags for language variants
- Creates localized metadata for each language
- Handles URL structure for multi-language content
- Implements language-specific sitemaps
- Manages translated content in Firebase
```

## Conversion Optimization

```prompt
Design a newsletter signup component that:
- Integrates with Firebase for data storage
- Implements proper form accessibility
- Uses conversion-optimized copy and design
- Adds tracking for signup conversion rates
- Segments users based on content preferences
```

```prompt
Create a related content algorithm that:
- Suggests relevant content based on categories and tags
- Implements proper internal linking structure
- Tracks click-through rates on recommendations
- Personalizes suggestions based on user behavior
- Prioritizes high-converting content
```