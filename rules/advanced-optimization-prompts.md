# İleri Seviye Optimizasyon Promptları - Next.js & Firebase

## Performans Optimizasyonu

```prompt
Oluştur bir Next.js performans optimizasyon stratejisi:
- Intersection Observer API ile lazy loading
- Web Workers kullanarak ağır işlemleri ana thread'den ayırma
- React Profiler ile render performansı analizi
- useMemo ve useCallback ile gereksiz render'ları önleme
- CSS-in-JS optimizasyonu (styled-components veya emotion)
- Font loading stratejisi ve font-display özelliği
- HTTP/2 ve HTTP/3 avantajlarından yararlanma
```

## Firebase Optimizasyonu

```prompt
Firebase veritabanı ve sorgu optimizasyonu stratejisi oluştur:
- Firestore sorgu desenlerini optimize etme
- Compound index'ler ile sorgu performansını artırma
- Firestore belge boyutunu küçültme teknikleri
- Denormalizasyon vs normalizasyon stratejileri
- Offline-first yaklaşımı ve yerel ö<PERSON>belleğe alma
- Batch işlemleri ve transaction'lar ile yazma performansını artırma
- Firestore Security Rules optimizasyonu
```

## Kullanıcı Deneyimi Optimizasyonu

```prompt
Kullanıcı deneyimini optimize eden bir Next.js stratejisi oluştur:
- Skeleton screens ile yükleme durumları
- Optimistic UI güncellemeleri
- Progresif geliştirme (progressive enhancement)
- Offline deneyim ve service worker entegrasyonu
- Gesture-based interactions ve animasyonlar
- Form validasyonu ve hata yönetimi
- Klavye navigasyonu ve erişilebilirlik
```

## Kod Kalitesi ve Bakım

```prompt
Next.js ve Firebase projesi için kod kalitesi stratejisi oluştur:
- TypeScript tip güvenliği ve interface tasarımı
- Unit ve integration test stratejisi (Jest, React Testing Library)
- E2E test stratejisi (Cypress, Playwright)
- Linting ve formatting kuralları (ESLint, Prettier)
- Git-hooks ve pre-commit kontrolleri
- Modüler kod mimarisi ve klasör yapısı
- Kod belgelendirme standartları
```

## State Yönetimi

```prompt
Next.js ve Firebase projesi için modern state yönetimi stratejisi oluştur:
- React Context API vs Redux vs Zustand vs Jotai karşılaştırması
- Server state ve client state ayrımı
- React Query veya SWR ile veri fetching
- Atom-based state yönetimi
- Form state yönetimi (Formik, React Hook Form)
- Global state persistance stratejileri
- State immerability ve değişmezlik prensipleri
```

## API Optimizasyonu

```prompt
Next.js API routes ve Firebase fonksiyonları için optimizasyon stratejisi:
- API route handler optimizasyonu
- Edge functions kullanımı
- Middleware implementasyonu
- Rate limiting ve throttling
- API caching stratejileri
- GraphQL vs REST API karşılaştırması
- API versiyonlama stratejisi
```

## Güvenlik Optimizasyonu

```prompt
Next.js ve Firebase projesi için kapsamlı güvenlik stratejisi:
- OWASP Top 10 güvenlik açıklarına karşı koruma
- JWT token yönetimi ve güvenliği
- CSRF ve XSS koruması
- Firebase Authentication güvenlik pratikleri
- API güvenliği ve input validasyonu
- Hassas veri şifreleme stratejileri
- Güvenlik denetimi ve penetrasyon testi
```

## Ölçeklenebilirlik

```prompt
Next.js ve Firebase uygulamasını ölçeklendirme stratejisi:
- Mikroservis mimarisi ve monorepo yaklaşımı
- Serverless fonksiyonların ölçeklendirilmesi
- Firestore sharding ve partitioning
- CDN ve edge caching stratejileri
- Çoklu bölge (multi-region) deployment
- Yük dengeleme ve otomatik ölçeklendirme
- Büyük veri setleri ile çalışma stratejileri
```

## Analitik ve İzleme

```prompt
Next.js ve Firebase projesi için kapsamlı analitik ve izleme stratejisi:
- Real User Monitoring (RUM) implementasyonu
- Custom event tracking ve kullanıcı davranış analizi
- Error tracking ve exception handling
- Performance monitoring ve bottleneck tespiti
- A/B test altyapısı ve feature flagging
- Kullanıcı oturum kaydı ve heatmap analizi
- Veri odaklı karar verme süreci
```

## PWA ve Mobil Optimizasyon

```prompt
Next.js projesini Progressive Web App (PWA) olarak optimize etme:
- Service Worker implementasyonu
- Manifest.json yapılandırması
- Offline functionality ve background sync
- Push notifications
- App shell mimarisi
- Install prompts ve home screen ekleme
- Responsive tasarım ve mobil-first yaklaşım
```

## Internationalization ve Localization

```prompt
Next.js projesi için kapsamlı i18n stratejisi:
- next-i18next veya next-intl implementasyonu
- Dinamik içerik çevirisi
- Çoklu dil desteği için veritabanı yapısı
- RTL dil desteği
- Tarih, saat ve para birimi formatlaması
- Çeviri workflow ve yönetimi
- SEO için dil-spesifik meta etiketleri
```